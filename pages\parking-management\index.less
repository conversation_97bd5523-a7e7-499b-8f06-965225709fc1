@import '/variable.less';

/* 主容器样式 */
.parking-management-container {
  min-height: 100vh;
  background-color: #F7F8FA;
  box-sizing: border-box;
  padding-bottom: 40rpx;
  position: relative;

  /* 下拉刷新样式优化 */
  .t-pull-down-refresh {
    &__track {
      background-color: transparent;
    }

    &__text {
      color: #999999;
      font-size: 26rpx;
    }
  }

  /* 头部主要内容 */
  .header-main {
    text-align: center;
    margin: 20rpx 0 40rpx;
    padding: 0 30rpx;
    position: relative;
    z-index: 2;

    .header-title {
      font-size: 36rpx;
      font-weight: 600;
      color: #FFFFFF;
      text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
      margin-bottom: 8rpx;
    }

    .spots-count {
      font-size: 26rpx;
      color: rgba(255, 255, 255, 0.9);
      font-weight: 400;
    }
  }

  /* 头部次要内容 */
  .header-secondary {
    position: relative;
    z-index: 2;
  }

  /* 车位统计信息 */
  .spots-info-section {
    margin: 10rpx auto 40rpx;
    text-align: center;
    position: relative;
    z-index: 2;

    .spots-info-tag {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      background-color: rgba(255, 255, 255, 0.15);
      color: #fff;
      font-size: 26rpx;
      padding: 8rpx 20rpx;
      border-radius: 24rpx;
      box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.05);

      t-icon {
        margin-right: 8rpx;
      }

      text {
        font-weight: 400;
      }
    }
  }

  /* 温馨提示区域 */
  .notice-section {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 12rpx;
    padding: 16rpx 20rpx;
    margin: 0 30rpx;
    position: relative;
    z-index: 2;
    backdrop-filter: blur(2rpx);
    box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.05);

    .notice-title {
      display: flex;
      align-items: center;
      font-size: 26rpx;
      color: #fff;
      font-weight: 500;
      margin-bottom: 12rpx;

      t-icon {
        margin-right: 8rpx;
      }
    }

    .notice-content {
      padding: 0;

      .notice-item {
        font-size: 24rpx;
        color: rgba(255, 255, 255, 0.85);
        line-height: 1.6;
        margin-bottom: 10rpx;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }

  /* 车位列表内容区域 */
  .parking-list-content {
    padding: 24rpx 24rpx 120rpx;
    position: relative;
    z-index: 1;
    margin-top: 30rpx; /* 增加顶部边距，与浮动头部保持距离 */
  }

  /* 车位列表 */
  .parking-list {
    padding: 0;
    position: relative;
    z-index: 1;
    transition: margin-top 0.3s ease; /* 添加过渡效果 */
  }

  /* 当头部折叠时，减少上边距 */
  &.header-collapsed .parking-list-content {
    margin-top: 180rpx;
  }

  /* 加载和空状态 */
  .loading-container, .empty-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 100rpx 0;
    background-color: #FFFFFF;
    border-radius: 30rpx; /* 更大的圆角，与卡片保持一致 */
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
    margin-bottom: 30rpx; /* 增加间距 */
  }

  .loading-text {
    margin-top: 16rpx;
    font-size: 26rpx;
    color: #959595; /* 统一颜色 */
  }

  /* 车位项 */
  .parking-item {
    background-color: #FFFFFF;
    border-radius: 12rpx;
    margin-bottom: 36rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
    overflow: hidden;

    /* 车位头部 */
    .spot-header {
      padding: 30rpx;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .spot-number {
        font-size: 32rpx;
        font-weight: 600;
        color: @gy1;
        flex: 1;
        position: relative;
        padding-left: 16rpx;

        &::before {
          content: '';
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 6rpx;
          height: 24rpx;
          background-color: @brand7-normal;
          border-radius: 3rpx;
        }
      }

      /* 状态标签容器 */
      .status-tags {
        display: flex;
        align-items: center;
        gap: 12rpx; /* 标签之间的间距稍微增加 */
        flex-wrap: wrap; /* 如果空间不够，允许换行 */
      }

      /* 状态标签 */
      .status-tag {
        display: flex;
        align-items: center;
        padding: 4rpx 14rpx;
        border-radius: 24rpx;
        font-size: 22rpx;
        height: 44rpx;
        box-sizing: border-box;
        box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.05);

        t-icon {
          margin-right: 8rpx;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        /* 所有标签统一使用类似 status-sharing 的样式 */
        background-color: rgba(7, 193, 96, 0.1);
        color: #07C160;
        border: 1rpx solid rgba(7, 193, 96, 0.2);

        &.status-using {
          background-color: rgba(0, 82, 217, 0.1);
          color: @brand7-normal;
          border: 1rpx solid rgba(0, 82, 217, 0.2);
        }

        &.status-unshared {
          background-color: rgba(144, 147, 153, 0.1);
          color: #909399;
          border: 1rpx solid rgba(144, 147, 153, 0.2);
        }

        /* 空闲标签也使用共享中的样式，只是透明度稍微降低 */
        &.status-free {
          background-color: rgba(7, 193, 96, 0.1);
          color: #07C160;
          border: 1rpx solid rgba(7, 193, 96, 0.2);
        }
      }
    }

    /* 分隔线 */
    .divider {
      height: 1rpx;
      background-color: #F0F0F0;
      margin: 6rpx 0;
      opacity: 0.8;
    }

    /* 车位详情 */
    .spot-details {
      padding: 0 30rpx 30rpx;

      /* 信息项样式 */
      .info-item {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        padding: 24rpx 0;
      }

      .info-label {
        font-size: 28rpx;
        color: @gy2;
        min-width: 140rpx;
      }

      .info-value {
        font-size: 28rpx;
        color: @gy1;
        text-align: right;
        flex: 1;
        line-height: 1.5;
      }

      /* 价格信息样式已合并到 info-item 中 */

      /* 操作按钮区域 */
      .action-buttons {
        display: flex;
        justify-content: flex-end;
        margin-top: 30rpx;
        padding-top: 20rpx;
        border-top: 1rpx solid #F0F0F0;
        gap: 16rpx; /* 添加按钮之间的间距 */

        /* 操作按钮 */
        .action-button {
          height: 72rpx;
          padding: 0 36rpx;
          border-radius: 36rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 28rpx;
          font-weight: 500;
          letter-spacing: 1rpx;
          box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);

          /* 所有按钮统一使用share-button的样式，只保留不同的背景颜色 */
          color: #FFFFFF;
          box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.15);
          border: none;

          &.contact-button {
            background-color: @brand7-normal; /* 蓝色 */
          }

          &.lock-button {
            background-color: #FA9D3B; /* 橙色 */
          }

          &.share-button {
            background-color: @brand7-normal; /* 蓝色 */
          }

          &:active {
            transform: translateY(2rpx);
            box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.05);
          }
        }
      }
    }
  }

  /* 加载状态 */
  .loading-wrapper {
    padding: 30rpx 0;
    text-align: center;
    color: @gy2;
  }

  /* 共享设置弹窗 */
  .share-popup {
    background-color: #FFFFFF;
    border-radius: 24rpx 24rpx 0 0;
    padding: 40rpx 30rpx;
    box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.08);
    position: relative;
    max-height: 90vh;
    overflow-y: auto;

    .popup-header {
      margin-bottom: 36rpx;
      position: relative;

      .popup-title {
        font-size: 34rpx;
        font-weight: 600;
        color: @gy1;
        margin-bottom: 8rpx;
        position: relative;
        padding-left: 16rpx;

        &::before {
          content: '';
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 6rpx;
          height: 28rpx;
          background-color: @brand7-normal;
          border-radius: 3rpx;
        }
      }

      .popup-subtitle {
        font-size: 26rpx;
        color: @gy2;
        padding-left: 16rpx;
      }
    }

    /* 表单项 */
    .form-item {
      margin-bottom: 36rpx;

      .form-label {
        font-size: 28rpx;
        color: @gy1;
        font-weight: 500;
        margin-bottom: 20rpx;
        display: flex;
        align-items: center;
      }

      /* 时间选择器 */
      .time-picker-container {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        margin-top: 12rpx;

        .time-picker {
          flex: 1;
          min-width: 160rpx;
          height: 80rpx;
          background-color: #F7F8FA;
          border-radius: 8rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 28rpx;
          color: @gy1;
          box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.05);
          transition: all 0.2s ease;
          border: 1rpx solid transparent;

          &.time-picker-error {
            background-color: rgba(227, 77, 89, 0.05);
            border: 1rpx solid rgba(227, 77, 89, 0.3);
            color: #E34D59;
          }

          &:active {
            opacity: 0.8;
            transform: translateY(2rpx);
          }
        }

        /* 日期类型选择器 */
        .day-picker {
          height: 80rpx;
          min-width: 120rpx;
          background-color: #F7F8FA;
          border-radius: 8rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 28rpx;
          color: @gy1;
          margin-right: 10rpx;
          box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.05);
          transition: all 0.2s ease;

          t-icon {
            margin-left: 6rpx;
          }

          &:active {
            opacity: 0.8;
            transform: translateY(2rpx);
          }
        }

        .time-separator {
          margin: 0 12rpx;
          font-size: 28rpx;
          color: @gy2;
        }
      }

      .time-duration {
        margin-top: 16rpx;
        font-size: 26rpx;
        color: @gy2;
        background-color: rgba(0, 82, 217, 0.05);
        padding: 8rpx 16rpx;
        border-radius: 6rpx;
        display: inline-block;
      }

      /* 价格输入 */
      .price-input-container {
        --td-input-bg-color: #F7F8FA;
        --td-input-border-radius: 8rpx;
        --td-input-vertical-padding: 24rpx;
        box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.05);

        &.price-input-error {
          --td-input-bg-color: rgba(227, 77, 89, 0.05);
          --td-input-border-color: rgba(227, 77, 89, 0.3);
        }
      }
    }

    /* 温馨提示 */
    .tips-section {
      background-color: #FFF7E6;
      border-radius: 12rpx;
      padding: 24rpx;
      margin-bottom: 40rpx;
      box-shadow: 0 2rpx 8rpx rgba(250, 157, 59, 0.1);

      .tips-title {
        font-size: 26rpx;
        color: #FA9D3B;
        font-weight: 500;
        margin-bottom: 12rpx;
        display: flex;
        align-items: center;

        &::before {
          content: '';
          display: inline-block;
          width: 8rpx;
          height: 8rpx;
          background-color: #FA9D3B;
          border-radius: 50%;
          margin-right: 8rpx;
        }
      }

      .tips-content {
        font-size: 24rpx;
        color: #FA9D3B;
        line-height: 1.8;
        padding-left: 16rpx;
      }
    }

    /* 右上角按钮 */
    .popup-right-button {
      position: absolute;
      top: 40rpx;
      right: 30rpx;
      z-index: 10;
    }

    /* 底部按钮 */
    .popup-buttons {
      display: flex;
      margin-top: 40rpx;

      .popup-button {
        flex: 1;
        height: 88rpx;
        border-radius: 44rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 30rpx;
        font-weight: 500;
        letter-spacing: 2rpx;
        transition: all 0.2s ease;

        &.cancel-button {
          background-color: #F7F8FA;
          color: @gy1;
          margin-right: 20rpx;
          border: 1rpx solid #EEEEEE;
        }

        &.confirm-button {
          background-color: @brand7-normal;
          color: #FFFFFF;
          box-shadow: 0 4rpx 8rpx rgba(0, 82, 217, 0.15);
        }

        &:active {
          transform: translateY(2rpx);
          box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.05);
        }
      }
    }
  }

  /* 确认信息弹窗 */
  .confirm-popup {
    background-color: #FFFFFF;
    border-radius: 24rpx 24rpx 0 0;
    padding: 40rpx 30rpx;
    box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.08);
    position: relative;
    max-height: 90vh;
    overflow-y: auto;

    .popup-header {
      margin-bottom: 36rpx;
      position: relative;

      .popup-title {
        font-size: 34rpx;
        font-weight: 600;
        color: @gy1;
        margin-bottom: 8rpx;
        position: relative;
        padding-left: 16rpx;

        &::before {
          content: '';
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 6rpx;
          height: 28rpx;
          background-color: @brand7-normal;
          border-radius: 3rpx;
        }
      }

      .popup-subtitle {
        font-size: 26rpx;
        color: @gy2;
        padding-left: 16rpx;
      }

      /* 右上角"仅保存不共享"按钮 */
      .save-only-button {
        position: absolute;
        top: 0;
        right: 0;
        font-size: 26rpx;
        color: @brand7-normal;
        padding: 8rpx 16rpx;
        cursor: pointer;
        transition: all 0.2s ease;
        border-radius: 30rpx;
        background-color: rgba(0, 82, 217, 0.05);
        border: 1rpx solid rgba(0, 82, 217, 0.1);

        &:active {
          opacity: 0.7;
          transform: translateY(2rpx);
        }
      }
    }

    .confirm-info {
      margin-bottom: 36rpx;
      background-color: #F7F8FA;
      border-radius: 12rpx;
      padding: 24rpx;
      box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.05);

      .confirm-item {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        padding: 16rpx 0;
        border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);

        &:last-child {
          border-bottom: none;
        }

        .confirm-label {
          font-size: 28rpx;
          color: @gy2;
          min-width: 140rpx;
        }

        .confirm-value {
          font-size: 28rpx;
          color: @gy1;
          text-align: right;
          flex: 1;
          font-weight: 500;
        }
      }
    }

    .confirm-divider {
      height: 1rpx;
      background-color: #F0F0F0;
      margin-bottom: 36rpx;
      opacity: 0.8;
    }

    /* 底部按钮 */
    .popup-buttons {
      display: flex;
      margin-top: 40rpx;

      .popup-button {
        flex: 1;
        height: 88rpx;
        border-radius: 44rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 30rpx;
        font-weight: 500;
        letter-spacing: 2rpx;
        transition: all 0.2s ease;

        &.cancel-button {
          background-color: #F7F8FA;
          color: @gy1;
          margin-right: 20rpx;
          border: 1rpx solid #EEEEEE;
        }

        &.confirm-button {
          background-color: @brand7-normal;
          color: #FFFFFF;
          box-shadow: 0 4rpx 8rpx rgba(0, 82, 217, 0.15);
        }

        &:active {
          transform: translateY(2rpx);
          box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.05);
        }
      }
    }
  }

  /* 锁操作弹窗 */
  .lock-operation-popup {
    background-color: #FFFFFF;
    border-radius: 24rpx;
    padding: 60rpx 40rpx 40rpx;
    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
    min-width: 560rpx;
    max-width: 80vw;

    .lock-operation-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;

      .lock-operation-icon {
        margin-bottom: 32rpx;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .lock-operation-text {
        font-size: 32rpx;
        color: @gy1;
        line-height: 1.5;
        margin-bottom: 40rpx;
        font-weight: 500;
      }

      .lock-operation-buttons {
        display: flex;
        gap: 20rpx;
        width: 100%;

        .lock-operation-button {
          flex: 1;
          height: 80rpx;
          border-radius: 40rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 28rpx;
          font-weight: 500;
          letter-spacing: 1rpx;
          transition: all 0.2s ease;

          &.cancel-btn {
            background-color: #F7F8FA;
            color: @gy2;
            box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
          }

          &.retry-btn {
            background-color: @brand7-normal;
            color: #FFFFFF;
            box-shadow: 0 4rpx 8rpx rgba(0, 82, 217, 0.3);
          }

          &:active {
            transform: translateY(2rpx);
            box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.05);
          }
        }
      }
    }
  }
}
