import request from '~/api/request';
import { walletApi, lockApi, plateApi } from '~/api/index';
const useLoginOverlay = require('../../behaviors/useLoginOverlay');
import useToastBehavior from '~/behaviors/useToast';

Page({
  behaviors: [useLoginOverlay, useToastBehavior],

  data: {
    isLoad: false,
    personalInfo: {
      image: '/static/my/avatar.png',
      name: '用户123456789',
      level: 9,
      balance: '0.00',
      spots: '5',
      cars: '7',
      income: '1280.50',
      bills: '12'
    },
    // 收入数据
    incomeData: {
      name: '车位收入',
      type: 'income',
      url: ''
    },
    // 收入数据
    spotsMana: {
      name: '车位管理',
      type: 'income',
      url: '/pages/parking-management/index'
    },
    // 账单数据
    billData: {
      name: '全部账单',
      type: 'bill',
      url: '/pages/parking-bill/index'
    },
    // 钱包数据
    walletData: {
      name: '账户钱包',
      type: 'wallet',
      url: ''
    },
    // 车辆管理数据
    plate: {
      name: '车牌管理',
      type: 'plate',
      url: '/pages/plate-management/index'
    },
    // 客服数据
    serviceData: {
      name: '联系客服',
      type: 'service',
      url: ''
    },
    // 反馈数据
    feedbackData: {
      name: '投诉建议',
      type: 'feedback',
      url: '/pages/feedback/index'
    }
  },

  onLoad() {
    // 初始化页面
  },

  async onShow() {
    const Token = wx.getStorageSync('access_token');

    if (Token) {
      try {
        // 并行获取用户相关数据
        const [walletData, lockCount, plateCount] = await Promise.allSettled([
          this.getWalletBalance(),
          this.getLockCount(),
          this.getPlateCount()
        ]);

        // 处理钱包余额数据
        const balance = walletData.status === 'fulfilled' && walletData.value
          ? walletData.value.toString()
          : '0.00';

        // 处理车位锁数量数据
        const spots = lockCount.status === 'fulfilled' && lockCount.value
          ? lockCount.value.toString()
          : '0';

        // 处理车牌数量数据
        const cars = plateCount.status === 'fulfilled' && plateCount.value
          ? plateCount.value.toString()
          : '0';

        this.setData({
          isLoad: true,
          personalInfo: {
            ...this.data.personalInfo,
            image: '/assets/avatar-Sean.png',
            name: '共享车位用户',
            level: 1,
            balance: balance,
            spots: spots,
            cars: cars
          }
        });
      } catch (error) {
        console.error('获取用户信息失败', error);
        this.showToast('获取用户信息失败');
      }
    }
  },

  // 获取钱包余额
  async getWalletBalance() {
    try {
      const res = await walletApi.apiGetWalletBalance();
      if (res && res.code === 0 && res.data) {
        return res.data.balance || '0.00';
      }
      return '0.00';
    } catch (error) {
      console.error('获取钱包余额失败:', error);
      return '0.00';
    }
  },

  // 获取车位锁数量
  async getLockCount() {
    try {
      const res = await lockApi.apiGetLockCount();
      if (res && res.code === 0 && res.data) {
        return res.data || '0';
      }
      return '0';
    } catch (error) {
      console.error('获取车位锁数量失败:', error);
      return '0';
    }
  },

  // 获取车牌数量
  async getPlateCount() {
    try {
      const res = await plateApi.apiGetPlateCount();
      if (res && res.code === 0 && res.data) {
        return res.data || '0';
      }
      return '0';
    } catch (error) {
      console.error('获取车牌数量失败:', error);
      return '0';
    }
  },

  async getPersonalInfo() {
    try {
      const info = await request('/api/genPersonalInfo').then((res) => res.data.data);
      return info;
    } catch (error) {
      console.error('API请求失败', error);
      return {};
    }
  },

  onLogin(e) {
    wx.navigateTo({
      url: '/pages/login/login',
    });
  },

  loginOut() {
    // 清除token
    wx.removeStorageSync('access_token');
    // 设置标记，确保用户进入首页时会看到登录弹窗
    wx.setStorageSync('show_login_overlay', true);
    // 使用switchTab跳转到首页，这比navigateBack更可靠
    wx.switchTab({
      url: '/pages/home-park/index',
      fail: (err) => {
        console.error('跳转到首页失败:', err);
        // 如果switchTab失败，尝试使用reLaunch
        wx.reLaunch({
          url: '/pages/home-park/index'
        });
      }
    });
  },

  onEleClick(e) {
    const { name, type, url } = e.currentTarget.dataset.data;

    // 如果有URL，跳转到对应页面
    if (url) {
      wx.navigateTo({ url });
      return;
    }
    // 根据类型处理不同的点击事件
    switch (type) {
      case 'service':
        // 联系客服
        this.contactCustomerService();
        break;
      case 'wallet':
        // 账户钱包
        this.showToast('钱包功能开发中');
        break;
      case 'income':
        // 车位收入
        this.showToast('收入功能开发中');
        break;
      default:
        // 默认显示提示
        this.showToast(name);
        break;
    }
  },

  // 联系客服
  contactCustomerService() {
    wx.makePhoneCall({
      phoneNumber: '************', // 客服电话号码
      success: () => {
        console.log('拨打客服电话成功');
      },
      fail: (error) => {
        console.error('拨打客服电话失败:', error);
        this.showToast('拨打客服电话失败');
      }
    });
  },

  // 显示提示信息
  showToast(message) {
    this.onShowToast('#t-toast', message);
  },
});
