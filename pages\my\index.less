@import '/variable.less';

.my-container {
  min-height: 100vh;
  background-color: #F7F8FA;
  padding-bottom: 30rpx;
  box-sizing: border-box;

  /* 顶部背景区域 */
  .my-header {
    background: linear-gradient(135deg, #0052D9 0%, #1867FF 100%);
    padding: 150rpx 30rpx 40rpx;
    color: #fff;
    position: relative;
    border-radius: 0 0 30rpx 30rpx;
    box-shadow: 0 10rpx 20rpx rgba(0, 82, 217, 0.15);
    overflow: hidden;

    /* 装饰性圆形元素 */
    .decoration-circle {
      position: absolute;
      border-radius: 50%;
      z-index: 1;

      &.top-right {
        top: -100rpx;
        right: -100rpx;
        width: 300rpx;
        height: 300rpx;
        background: rgba(255, 255, 255, 0.1);
      }

      &.bottom-left {
        bottom: -80rpx;
        left: -80rpx;
        width: 200rpx;
        height: 200rpx;
        background: rgba(255, 255, 255, 0.08);
      }

      &.bottom-right {
        bottom: 60rpx;
        right: 40rpx;
        width: 120rpx;
        height: 120rpx;
        background: rgba(255, 255, 255, 0.05);
      }
    }
  }

  /* 用户信息区域 */
  .user-info {
    display: flex;
    align-items: center;
    position: relative;
    z-index: 2;
    margin-bottom: 40rpx;

    .avatar-container {
      position: relative;
      margin-right: 24rpx;

      .avatar {
        width: 120rpx;
        height: 120rpx;
        border-radius: 50%;
        border: 3rpx solid #FFFFFF;
        background-color: #D3D3D3;
      }

      .level-tag {
        position: absolute;
        bottom: 0;
        right: 0;
        background-color: #0052D9;
        color: #FFFFFF;
        font-size: 20rpx;
        padding: 4rpx 10rpx;
        border-radius: 20rpx;
        border: 2rpx solid #FFFFFF;
      }
    }

    .user-details {
      flex: 1;

      .username {
        font-size: 36rpx;
        font-weight: 500;
        margin-bottom: 8rpx;
        color: #FFFFFF;
      }

      .login-btn {
        font-size: 28rpx;
        color: rgba(255, 255, 255, 0.9);
        background: rgba(255, 255, 255, 0.2);
        padding: 8rpx 16rpx;
        border-radius: 30rpx;
        display: inline-block;
      }
    }

    .edit-icon {
      padding: 10rpx;
    }
  }

  /* 账户信息区域 */
  .account-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(255, 255, 255, 0.15);
    border-radius: 16rpx;
    padding: 0;
    backdrop-filter: blur(10rpx);
    margin: 0 10rpx;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 2;

    .account-item {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 20rpx 0;
      text-align: center;

      .account-value {
        font-size: 36rpx;
        font-weight: 600;
        margin-bottom: 8rpx;
        color: #FFFFFF;
        text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
      }

      .account-label {
        font-size: 24rpx;
        color: rgba(255, 255, 255, 0.9);
      }
    }

    .account-divider {
      width: 2rpx;
      height: 60rpx;
      background-color: rgba(255, 255, 255, 0.3);
    }
  }

  /* 内容区域 */
  .my-content {
    padding: 30rpx 24rpx;

    /* 卡片通用样式 */
    .card {
      background-color: #FFFFFF;
      border-radius: 12rpx;
      margin-bottom: 24rpx;
      box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
      overflow: hidden;

      .card-item {
        display: flex;
        align-items: center;
        padding: 24rpx;
        position: relative;

        .card-icon {
          width: 48rpx;
          height: 48rpx;
          margin-right: 16rpx;
        }

        .card-text {
          flex: 1;
          font-size: 30rpx;
          color: #333333;
        }

        .card-arrow {
          margin-left: 8rpx;
        }
      }

      .card-divider {
        height: 1rpx;
        background-color: #F0F0F0;
        margin: 0 24rpx;
      }
    }

    /* 收入和账单卡片 */
    .income-bill-card {
      padding: 0;

      .income-bill-row {
        display: flex;
        width: 100%;

        .income-bill-item {
          flex: 1;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          padding: 30rpx 24rpx;
          position: relative;

          .income-bill-icon {
            width: 60rpx;
            height: 60rpx;
            margin-bottom: 16rpx;
          }

          .income-bill-text {
            font-size: 28rpx;
            color: #333333;
            margin-bottom: 12rpx;
          }

          .income-bill-value {
            font-size: 32rpx;
            color: @brand7-normal;
            font-weight: 500;
          }

          &:active {
            background-color: rgba(0, 0, 0, 0.05);
          }
        }

        .income-bill-divider {
          width: 2rpx;
          height: 80rpx;
          background-color: #F0F0F0;
          align-self: center;
        }
      }
    }
  }
}
