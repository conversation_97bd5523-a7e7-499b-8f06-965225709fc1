
import Message from 'tdesign-miniprogram/message/index';
import { parkApi, orderApi } from '~/api/index';
import { formatDistance, sortArrBySlots } from '~/utils/util'
// Import the login overlay behavior
const useLoginOverlay = require('../../behaviors/useLoginOverlay');

Page({
  // Use the login overlay behavior
  behaviors: [useLoginOverlay],

  data: {
    swiperList: [
      'https://share-park.oss-cn-hangzhou.aliyuncs.com//swiper/swiper1.jpg',
      'https://share-park.oss-cn-hangzhou.aliyuncs.com//swiper/swiper2.jpg',
      'https://share-park.oss-cn-hangzhou.aliyuncs.com//swiper/swiper3.jpg'
    ],
    nearbyParkingList: [],
    nearbyParkingLoading: true, // 添加加载状态
    // 车锁订单状态
    lockHasOrderDesc: '', // 个人车锁是否有订单的描述
    usingOrdersDesc: '', // 当前用户订单状态的描述
  },
  // 生命周期
  async onReady() {
    // 注册 t-message 组件
    this.message = this.selectComponent('#t-message');

    // const [swiperRes] = await Promise.all([
    //   request('/home-park/swipers').then((res) => res.data),
    // ]);
    // this.setData({
    //   swiperList: swiperRes.data,
    // });
  },
  onLoad() {
    console.log('home-park 页面 onLoad');
    // 获取并排序附近车场列表
    this.refNearlyPark();
    // 获取订单状态
    this.fetchOrderStatus();
  },

  onShow() {
    // 页面显示时的逻辑
    if (typeof this.getTabBar === 'function') {
      this.getTabBar().setData({
        value: 'home-park'
      });
    }
    // 检查登录状态和登录弹窗标记
    this.checkLoginOverlayFlag();

    // 如果有登录错误消息，显示提示
    const loginErrorMessage = wx.getStorageSync('login_error_message');
    if (loginErrorMessage) {
      this.showMessage(loginErrorMessage, 'error');
      wx.removeStorageSync('login_error_message');
    }

    // 刷新订单状态
    // this.fetchOrderStatus();
  },
  navigateToPage(e) {
    const url = e.currentTarget.dataset.navto;
    this.onPageTap(() => {
      wx.navigateTo({ url });
    })
  },
  onPullDownRefresh() {
    // 刷新附近车场数据
    this.refNearlyPark();
    setTimeout(() => {
      wx.stopPullDownRefresh();
      this.showMessage('刷新成功', { placement: 'center' });
    }, 1000);
  },


  // 获取订单状态
  async fetchOrderStatus() {
    try {
      // 并行请求两个接口
      const [lockHasOrderRes, usingOrdersRes] = await Promise.all([
        orderApi.apiCheckLockHasOrder(false),
        orderApi.apiGetUsingOrders(false)
      ]);

      // 处理个人车锁是否有订单的状态
      let lockHasOrderDesc = '';
      if (lockHasOrderRes) {
        lockHasOrderDesc = '使用中';
      } else {
        lockHasOrderDesc = '未使用';
      }

      // 处理当前用户订单状态
      let usingOrdersDesc = '';
      if (usingOrdersRes && usingOrdersRes.data.length > 0) {
        usingOrdersDesc = `${usingOrdersRes.data.length}个订单`;
      } else {
        usingOrdersDesc = '暂无订单';
      }

      // 更新页面数据
      this.setData({
        lockHasOrderDesc,
        usingOrdersDesc
      });

    } catch (error) {
      console.error('获取订单状态失败:', error);
      // 设置默认状态
      this.setData({
        lockHasOrderDesc: '请登录',
        usingOrdersDesc: '请登录'
      });
    }
  },

  refNearlyPark() {
    // 设置加载状态
    this.setData({
      nearbyParkingLoading: true
    });

    const apiRes = parkApi.apiGetNearbyParks();
    apiRes.then(res => {
      let results = res['data']['results']
      if (res.code === 0 && results && results.length > 0) {
        // 根据可用车位数量进行降序排序
        results = sortArrBySlots(results, 'availableSlots')
        console.log(results);
        const newParkingList = results.map(item => ({
          ...item,
          distance: formatDistance(item.distance)
        }));
        this.setData({
          nearbyParkingList: newParkingList,
          nearbyParkingLoading: false // 加载完成
        });

        // 显示排序成功的消息
        // this.showMessage('已按可用车位数排序');
      } else {
        this.setData({
          nearbyParkingList: [],
          nearbyParkingLoading: false // 加载完成
        });
        if (res.code !== 0) {
          this.showMessage('获取附近车场失败', 'error');
        } else if (!results || results.length === 0) {
          this.showMessage('附近暂无车场信息', 'warning');
        }
      }
    }).catch(err => {
      console.log(err);
      this.setData({
        nearbyParkingLoading: false // 加载失败也要关闭加载状态
      });
      this.showMessage('获取附近车场失败', 'error');
    })
  },

  onParkingInfoTap() {
    wx.navigateTo({
      url: `/pages/parking-detail/index`
    });
  },

  onOrderInfoTap() {
    wx.navigateTo({
      url: `/pages/order-detail/index`
    });
  },

  onParkingItemTap(e) {
    const id = e.currentTarget.dataset.id;
    // Navigate to the parking list page
    this.onPageTap(() => {
      wx.navigateTo({
        url: `/pages/parking-spots/index?id=${id}`
      });
    })
  },

  showMessage(message, type = 'info') {
    Message[type]({
      context: this,
      offset: ['190rpx', 32],
      duration: 2000,
      content: message,
    });
  },

  // Add a tap handler for the entire page
  onPageTap(callback) {
    // Check if user is logged in before proceeding
    this.checkLoginBeforeAction(callback);
  },

  // 处理登录成功事件
  handleLoginSuccess(e) {
    console.log('登录成功事件', e);
    // 如果登录成功，执行之前保存的回调
    if (e.detail && e.detail.success) {
      // 确保登录弹窗关闭
      this.setData({
        loginOverlayVisible: false
      });

      // 调用 behavior 中定义的方法执行之前保存的回调
      this.executePendingCallback();
    }
  }
});

